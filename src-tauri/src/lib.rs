// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use tauri::tray::{TrayIconBuilder, TrayIconEvent};

// 核心模块
pub mod hlc;
pub mod models;
pub mod storage_engine;
pub mod cloud_sync;
pub mod blob_manager;
pub mod new_sync_engine;
pub mod storage_adapter;

// 使用新的统一架构
use models::{ClipboardItem, StorageStats};
use storage_adapter::StorageConfig;
use new_sync_engine::NewSyncEngine;

// 统一的同步引擎类型
type SyncEngineContainer = Arc<Mutex<Option<Arc<NewSyncEngine>>>>;

// 剪贴板相关命令
#[tauri::command]
async fn get_clipboard_history(state: tauri::State<'_, SyncEngineContainer>) -> Result<Vec<ClipboardItem>, String> {
    let engine = {
        if let Ok(container) = state.lock() {
            container.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };

    if let Some(engine) = engine {
        engine.get_all_items().await.map_err(|e| e.to_string())
    } else {
        Ok(Vec::new())
    }
}

#[tauri::command]
async fn add_clipboard_item(content: String, _content_type: String, state: tauri::State<'_, SyncEngineContainer>) -> Result<(), String> {
    let engine = {
        if let Ok(container) = state.lock() {
            container.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };

    if let Some(engine) = engine {
        engine.add_text(&content).await.map_err(|e| e.to_string())
    } else {
        Err("同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn delete_clipboard_item(item_id: String, state: tauri::State<'_, SyncEngineContainer>) -> Result<(), String> {
    let engine = {
        if let Ok(container) = state.lock() {
            container.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };

    if let Some(engine) = engine {
        let _deleted = engine.delete_item(&item_id).await.map_err(|e| e.to_string())?;
        Ok(())
    } else {
        Err("同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn clear_clipboard_history(state: tauri::State<'_, SyncEngineContainer>) -> Result<(), String> {
    let engine = {
        if let Ok(container) = state.lock() {
            container.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };

    if let Some(engine) = engine {
        engine.clear_all().await.map_err(|e| e.to_string())
    } else {
        Err("同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn get_storage_stats(state: tauri::State<'_, SyncEngineContainer>) -> Result<StorageStats, String> {
    let engine = {
        if let Ok(container) = state.lock() {
            container.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };

    if let Some(engine) = engine {
        engine.get_stats().await.map_err(|e| e.to_string())
    } else {
        Ok(StorageStats::default())
    }
}

// 同步相关命令
#[tauri::command]
async fn sync_now(state: tauri::State<'_, SyncEngineContainer>) -> Result<(), String> {
    let engine = {
        if let Ok(container) = state.lock() {
            container.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };

    if let Some(engine) = engine {
        engine.sync().await.map_err(|e| e.to_string())
    } else {
        Err("同步引擎未初始化".to_string())
    }
}

#[tauri::command]
async fn get_sync_status(state: tauri::State<'_, SyncEngineContainer>) -> Result<serde_json::Value, String> {
    let engine = {
        if let Ok(container) = state.lock() {
            container.clone()
        } else {
            return Err("无法获取同步引擎".to_string());
        }
    };

    if let Some(engine) = engine {
        engine.get_sync_status().await.map_err(|e| e.to_string())
    } else {
        // 返回未初始化状态
        Ok(serde_json::json!({
            "item_count": 0,
            "is_syncing": false,
            "initialized": false
        }))
    }
}

// 配置相关命令
#[tauri::command]
async fn configure_storage(config: serde_json::Value) -> Result<(), String> {
    let storage_config: StorageConfig = serde_json::from_value(config)
        .map_err(|e| format!("配置解析失败: {}", e))?;
    
    storage_config.save().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_storage_config() -> Result<Option<serde_json::Value>, String> {
    match StorageConfig::load().await {
        Ok(config) => Ok(Some(serde_json::to_value(config).unwrap())),
        Err(_) => Ok(None),
    }
}

#[tauri::command]
async fn test_storage_connection(config: serde_json::Value) -> Result<bool, String> {
    let storage_config: StorageConfig = serde_json::from_value(config)
        .map_err(|e| format!("配置解析失败: {}", e))?;
    
    storage_config.test_connection().await.map_err(|e| e.to_string())
}

/// 获取应用配置目录，跨平台适配
fn get_app_data_dir() -> PathBuf {
    if let Some(config_dir) = dirs::config_dir() {
        config_dir.join("clippy")
    } else if let Some(home_dir) = dirs::home_dir() {
        home_dir.join(".clippy")
    } else {
        PathBuf::from("./clippy_data")
    }
}

/// 获取或创建设备ID
fn get_or_create_device_id() -> String {
    let config_dir = get_app_data_dir();
    let device_id_file = config_dir.join("device_id");
    
    if let Ok(device_id) = std::fs::read_to_string(&device_id_file) {
        device_id.trim().to_string()
    } else {
        let device_id = uuid::Uuid::new_v4().to_string();
        let _ = std::fs::create_dir_all(&config_dir);
        let _ = std::fs::write(&device_id_file, &device_id);
        device_id
    }
}

async fn load_storage_config() -> anyhow::Result<StorageConfig> {
    StorageConfig::load().await
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    let sync_engine_container: SyncEngineContainer = Arc::new(Mutex::new(None));

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .manage(sync_engine_container.clone())
        .setup(move |app| {
            // 异步初始化同步引擎
            let app_handle = app.handle().clone();
            let storage_dir = get_app_data_dir();
            let cache_dir = storage_dir.join("cache");
            let device_id = get_or_create_device_id();
            let sync_container = sync_engine_container.clone();

            tauri::async_runtime::spawn(async move {
                if let Ok(storage_config) = load_storage_config().await {
                    if let Ok(cloud_operator) = storage_config.create_operator().await {
                        let user_id = std::env::var("CLIPPY_USER_ID").unwrap_or_else(|_| "default_user".to_string());

                        match NewSyncEngine::new(
                            storage_dir,
                            cache_dir,
                            cloud_operator,
                            user_id,
                            device_id,
                        ).await {
                            Ok(new_sync_engine) => {
                                let new_sync_engine = Arc::new(new_sync_engine);

                                if let Err(e) = new_sync_engine.initialize().await {
                                    tracing::error!("同步引擎初始化失败: {}", e);
                                } else {
                                    tracing::info!("同步引擎初始化成功");

                                    // 启动后台同步任务
                                    let sync_engine_clone = new_sync_engine.clone();
                                    tokio::spawn(async move {
                                        if let Err(e) = sync_engine_clone.start_background_sync(15).await {
                                            tracing::error!("后台同步任务失败: {}", e);
                                        }
                                    });

                                    // 保存到全局状态
                                    if let Ok(mut container) = sync_container.lock() {
                                        *container = Some(new_sync_engine);
                                    }
                                }
                            }
                            Err(e) => {
                                tracing::error!("同步引擎创建失败: {}", e);
                            }
                        }
                    } else {
                        tracing::info!("未配置云存储，跳过同步引擎初始化");
                    }
                } else {
                    tracing::info!("未找到存储配置，跳过同步引擎初始化");
                }
            });

            // 创建系统托盘
            let _tray = TrayIconBuilder::new()
                .icon(app.default_window_icon().unwrap().clone())
                .title("Clippy - 剪贴板管理器")
                .tooltip("Clippy - 剪贴板管理器")
                .on_tray_icon_event(|tray, event| {
                    if let TrayIconEvent::Click { .. } = event {
                        if let Some(window) = tray.app_handle().get_webview_window("main") {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                })
                .build(app)?;

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_clipboard_history,
            add_clipboard_item,
            delete_clipboard_item,
            clear_clipboard_history,
            get_storage_stats,
            sync_now,
            get_sync_status,
            configure_storage,
            get_storage_config,
            test_storage_connection,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
