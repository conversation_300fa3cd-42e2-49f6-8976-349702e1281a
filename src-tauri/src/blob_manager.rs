use anyhow::{Context, Result};
use opendal::Operator;
use sha2::{Digest, Sha256};
use std::path::PathBuf;
use std::collections::HashMap;
use tokio::sync::RwLock;
use uuid::Uuid;

use crate::models::{BlobManifest, BlobChunk, ClipboardItem, ContentType};
use crate::hlc::HLC;

/// BLOB 管理器 - 支持内联存储、单对象存储、分块存储的混合架构
///
/// 基于内容寻址的去重存储，支持：
/// - 小文件 (<256KB): 内联存储在数据库中
/// - 中等文件 (256KB-8MB): 单对象存储
/// - 大文件 (>8MB): 分块存储，支持断点续传
pub struct BlobManager {
    cloud_operator: Operator,
    cache_dir: PathBuf,
    user_id: String,
    blob_cache: RwLock<HashMap<String, BlobManifest>>, // blob_id -> manifest
    chunk_cache: RwLock<HashMap<String, Vec<BlobChunk>>>, // blob_id -> chunks
}

impl BlobManager {
    /// 创建新的 BLOB 管理器
    pub fn new(cloud_operator: Operator, cache_dir: PathBuf, user_id: String) -> Self {
        Self {
            cloud_operator,
            cache_dir,
            user_id,
            blob_cache: RwLock::new(HashMap::new()),
            chunk_cache: RwLock::new(HashMap::new()),
        }
    }

    /// 处理文本内容
    pub async fn process_text(&self, text: &str, device_id: String, hlc: HLC) -> Result<ClipboardItem> {
        let content_bytes = text.as_bytes();
        let size = content_bytes.len() as u64;

        // 文本内容通常较小，直接内联存储
        if size <= 256 * 1024 {
            Ok(ClipboardItem::new_text(text.to_string(), device_id, hlc))
        } else {
            // 大文本，使用 BLOB 存储
            let blob_id = self.generate_blob_id(content_bytes);
            self.upload_blob(content_bytes, "text/plain", None, &blob_id).await?;

            Ok(ClipboardItem::new(
                Uuid::new_v4().to_string(),
                ContentType::Text,
                None,
                "text/plain".to_string(),
                hlc,
                device_id,
                size,
                Some(blob_id),
                None,
            ))
        }
    }

    /// 处理图片内容
    pub async fn process_image(&self, image_data: &[u8], format: &str, device_id: String, hlc: HLC) -> Result<ClipboardItem> {
        let size = image_data.len() as u64;

        if size <= 256 * 1024 {
            // 小图片，内联存储
            Ok(ClipboardItem::new_image(image_data.to_vec(), format.to_string(), device_id, hlc))
        } else {
            // 大图片，使用 BLOB 存储
            let blob_id = self.generate_blob_id(image_data);
            let mime = format!("image/{}", format);
            self.upload_blob(image_data, &mime, None, &blob_id).await?;

            Ok(ClipboardItem::new(
                Uuid::new_v4().to_string(),
                ContentType::Image,
                None,
                mime,
                hlc,
                device_id,
                size,
                Some(blob_id),
                None,
            ))
        }
    }

    /// 处理文件内容
    pub async fn process_file(&self, file_path: &str, device_id: String, hlc: HLC) -> Result<ClipboardItem> {
        let file_data = tokio::fs::read(file_path).await
            .context("读取文件失败")?;
        let size = file_data.len() as u64;

        let file_name = std::path::Path::new(file_path)
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown")
            .to_string();

        let mime = infer::get(&file_data)
            .map(|t| t.mime_type().to_string())
            .unwrap_or_else(|| "application/octet-stream".to_string());

        if size <= 256 * 1024 {
            // 小文件，内联存储
            Ok(ClipboardItem::new_file(file_path.to_string(), file_data, device_id, hlc))
        } else {
            // 大文件，使用 BLOB 存储
            let blob_id = self.generate_blob_id(&file_data);
            self.upload_blob(&file_data, &mime, Some(file_name.clone()), &blob_id).await?;

            Ok(ClipboardItem::new(
                Uuid::new_v4().to_string(),
                ContentType::File,
                None,
                mime,
                hlc,
                device_id,
                size,
                Some(blob_id),
                Some(file_name),
            ))
        }
    }



    /// 上传 BLOB 到云端（内容寻址，自动去重）
    async fn upload_blob(&self, content: &[u8], mime: &str, file_name: Option<String>, blob_id: &str) -> Result<BlobManifest> {
        let size = content.len() as u64;
        let sha256 = self.calculate_sha256(content);

        // 检查是否已存在（内容寻址去重）
        if let Some(existing_manifest) = self.get_blob_manifest(blob_id).await? {
            tracing::info!("BLOB {} 已存在，跳过上传", blob_id);
            return Ok(existing_manifest);
        }

        let manifest = if size > 8 * 1024 * 1024 { // 8 MiB，分块上传
            self.upload_chunked_blob(content, mime, file_name, blob_id, &sha256).await?
        } else {
            // 单对象上传
            self.upload_single_blob(content, mime, file_name, blob_id, &sha256).await?
        };

        // 缓存 manifest
        {
            let mut cache = self.blob_cache.write().await;
            cache.insert(blob_id.to_string(), manifest.clone());
        }

        Ok(manifest)
    }

    /// 单对象上传
    async fn upload_single_blob(
        &self,
        content: &[u8],
        mime: &str,
        file_name: Option<String>,
        blob_id: &str,
        sha256: &str,
    ) -> Result<BlobManifest> {
        let blob_path = self.get_blob_path(blob_id, None);
        let content_vec = content.to_vec(); // 复制内容以避免生命周期问题

        // 上传内容
        self.cloud_operator
            .write(&blob_path, content_vec)
            .await
            .context("上传 BLOB 失败")?;

        // 创建 manifest
        let manifest = BlobManifest::new(
            blob_id.to_string(),
            sha256.to_string(),
            content.len() as u64,
            mime.to_string(),
            file_name,
        );

        // 上传 manifest
        let manifest_path = self.get_manifest_path(blob_id);
        let manifest_data = serde_json::to_vec(&manifest)?;
        self.cloud_operator
            .write(&manifest_path, manifest_data)
            .await?;

        tracing::info!("单对象 BLOB 上传完成: {}", blob_path);
        Ok(manifest)
    }

    /// 分块上传（支持断点续传）
    async fn upload_chunked_blob(
        &self,
        content: &[u8],
        mime: &str,
        file_name: Option<String>,
        blob_id: &str,
        sha256: &str,
    ) -> Result<BlobManifest> {
        const CHUNK_SIZE: usize = 5 * 1024 * 1024; // 5 MiB per chunk

        let mut chunks = Vec::new();
        let total_chunks = (content.len() + CHUNK_SIZE - 1) / CHUNK_SIZE;

        // 上传每个分块
        for (i, chunk_data) in content.chunks(CHUNK_SIZE).enumerate() {
            let chunk_path = self.get_blob_path(blob_id, Some(i));
            let chunk_vec = chunk_data.to_vec(); // 复制分块数据以避免生命周期问题

            // 上传分块
            self.cloud_operator
                .write(&chunk_path, chunk_vec)
                .await
                .context(format!("上传分块 {} 失败", i))?;

            chunks.push(BlobChunk {
                blob_id: blob_id.to_string(),
                chunk_index: i as u32,
                etag: format!("chunk-{}", i), // 简化的 ETag
                size: chunk_data.len() as u64,
            });

            tracing::debug!("上传分块 {}/{}: {}", i + 1, total_chunks, chunk_path);
        }

        // 创建 manifest
        let manifest = BlobManifest::new(
            blob_id.to_string(),
            sha256.to_string(),
            content.len() as u64,
            mime.to_string(),
            file_name,
        );

        // 缓存分块信息
        {
            let mut chunk_cache = self.chunk_cache.write().await;
            chunk_cache.insert(blob_id.to_string(), chunks);
        }

        // 上传 manifest
        let manifest_path = self.get_manifest_path(blob_id);
        let manifest_data = serde_json::to_vec(&manifest)?;
        self.cloud_operator
            .write(&manifest_path, manifest_data)
            .await?;

        tracing::info!("分块 BLOB 上传完成: {} 个分块", total_chunks);
        Ok(manifest)
    }



    /// 下载 BLOB
    async fn download_blob(&self, blob_id: &str) -> Result<Vec<u8>> {
        // 检查本地缓存
        if let Some(cached_content) = self.get_cached_blob(blob_id).await? {
            return Ok(cached_content);
        }

        // 获取 manifest
        let manifest = self.get_blob_manifest(blob_id).await?
            .ok_or_else(|| anyhow::anyhow!("BLOB manifest 不存在: {}", blob_id))?;

        let content = if manifest.needs_chunking() {
            // 下载分块内容
            self.download_chunked_blob(blob_id).await?
        } else {
            // 下载单对象
            self.download_single_blob(blob_id).await?
        };

        // 验证 SHA256
        let actual_sha256 = self.calculate_sha256(&content);
        if actual_sha256 != manifest.sha256 {
            return Err(anyhow::anyhow!("BLOB SHA256 校验失败"));
        }

        // 缓存到本地
        self.cache_blob(blob_id, &content).await?;

        Ok(content)
    }

    /// 下载单对象
    async fn download_single_blob(&self, blob_id: &str) -> Result<Vec<u8>> {
        let blob_path = self.get_blob_path(blob_id, None);
        let data = self.cloud_operator
            .read(&blob_path)
            .await
            .context("下载单对象 BLOB 失败")?;
        Ok(data.to_vec())
    }

    /// 下载分块内容
    async fn download_chunked_blob(&self, blob_id: &str) -> Result<Vec<u8>> {
        // 获取分块信息
        let chunks = self.get_blob_chunks(blob_id).await?;
        let mut content = Vec::new();

        // 按顺序下载所有分块
        for chunk in chunks {
            let chunk_path = self.get_blob_path(blob_id, Some(chunk.chunk_index as usize));
            let chunk_data = self.cloud_operator
                .read(&chunk_path)
                .await
                .context(format!("下载分块 {} 失败", chunk.chunk_index))?;

            content.extend_from_slice(&chunk_data);
        }

        Ok(content)
    }

    /// 获取 BLOB 分块信息
    async fn get_blob_chunks(&self, blob_id: &str) -> Result<Vec<BlobChunk>> {
        // 检查缓存
        {
            let cache = self.chunk_cache.read().await;
            if let Some(chunks) = cache.get(blob_id) {
                return Ok(chunks.clone());
            }
        }

        // 从云端获取 manifest 并推断分块
        let manifest = self.get_blob_manifest(blob_id).await?
            .ok_or_else(|| anyhow::anyhow!("BLOB manifest 不存在: {}", blob_id))?;

        if !manifest.needs_chunking() {
            return Ok(Vec::new());
        }

        // 推断分块信息（基于文件大小）
        const CHUNK_SIZE: u64 = 5 * 1024 * 1024; // 5 MiB per chunk
        let total_chunks = (manifest.size + CHUNK_SIZE - 1) / CHUNK_SIZE;

        let mut chunks = Vec::new();
        for i in 0..total_chunks {
            let chunk_size = if i == total_chunks - 1 {
                manifest.size - i * CHUNK_SIZE
            } else {
                CHUNK_SIZE
            };

            chunks.push(BlobChunk {
                blob_id: blob_id.to_string(),
                chunk_index: i as u32,
                etag: format!("chunk-{}", i),
                size: chunk_size,
            });
        }

        // 缓存分块信息
        {
            let mut cache = self.chunk_cache.write().await;
            cache.insert(blob_id.to_string(), chunks.clone());
        }

        Ok(chunks)
    }

    /// 获取 BLOB manifest
    async fn get_blob_manifest(&self, blob_id: &str) -> Result<Option<BlobManifest>> {
        // 检查缓存
        {
            let cache = self.blob_cache.read().await;
            if let Some(manifest) = cache.get(blob_id) {
                return Ok(Some(manifest.clone()));
            }
        }

        // 从云端下载
        let manifest_path = self.get_manifest_path(blob_id);
        match self.cloud_operator.read(&manifest_path).await {
            Ok(data) => {
                let bytes = data.to_vec();
                let manifest: BlobManifest = serde_json::from_slice(&bytes)?;
                
                // 缓存
                {
                    let mut cache = self.blob_cache.write().await;
                    cache.insert(blob_id.to_string(), manifest.clone());
                }
                
                Ok(Some(manifest))
            }
            Err(_) => Ok(None), // manifest 不存在
        }
    }

    /// 获取本地缓存的 BLOB
    async fn get_cached_blob(&self, blob_id: &str) -> Result<Option<Vec<u8>>> {
        let cache_path = self.cache_dir.join(format!("{}.blob", blob_id));
        if cache_path.exists() {
            let content = tokio::fs::read(&cache_path).await?;
            Ok(Some(content))
        } else {
            Ok(None)
        }
    }

    /// 缓存 BLOB 到本地
    async fn cache_blob(&self, blob_id: &str, content: &[u8]) -> Result<()> {
        // 确保缓存目录存在
        tokio::fs::create_dir_all(&self.cache_dir).await?;
        
        let cache_path = self.cache_dir.join(format!("{}.blob", blob_id));
        tokio::fs::write(&cache_path, content).await?;
        
        tracing::debug!("BLOB 已缓存到本地: {}", cache_path.display());
        Ok(())
    }

    /// 生成 BLOB ID（基于内容 SHA256）
    fn generate_blob_id(&self, content: &[u8]) -> String {
        let hash = self.calculate_sha256(content);
        hash[..32].to_string() // 取前 32 字符
    }

    /// 计算 SHA256
    fn calculate_sha256(&self, content: &[u8]) -> String {
        let mut hasher = Sha256::new();
        hasher.update(content);
        format!("{:x}", hasher.finalize())
    }

    /// 获取 BLOB 存储路径
    fn get_blob_path(&self, blob_id: &str, chunk_index: Option<usize>) -> String {
        let prefix = &blob_id[..2]; // 前两个字符作为目录
        if let Some(index) = chunk_index {
            format!("{}/blob/{}/{}/chunk-{}", self.user_id, prefix, blob_id, index)
        } else {
            // 推断文件扩展名
            let ext = "dat"; // 默认扩展名，可以根据 MIME 类型推断
            format!("{}/blob/{}/{}.{}", self.user_id, prefix, blob_id, ext)
        }
    }

    /// 获取 manifest 路径
    fn get_manifest_path(&self, blob_id: &str) -> String {
        let prefix = &blob_id[..2];
        format!("{}/blob/{}/{}/manifest.json", self.user_id, prefix, blob_id)
    }

    /// 清理本地缓存
    pub async fn cleanup_cache(&self, max_size_bytes: u64) -> Result<()> {
        let mut cache_files = Vec::new();
        let mut total_size = 0u64;

        // 扫描缓存目录
        let mut entries = tokio::fs::read_dir(&self.cache_dir).await?;
        while let Some(entry) = entries.next_entry().await? {
            if let Ok(metadata) = entry.metadata().await {
                if metadata.is_file() && entry.file_name().to_string_lossy().ends_with(".blob") {
                    let size = metadata.len();
                    let modified = metadata.modified().unwrap_or(std::time::SystemTime::UNIX_EPOCH);
                    cache_files.push((entry.path(), size, modified));
                    total_size += size;
                }
            }
        }

        if total_size <= max_size_bytes {
            return Ok(());
        }

        // 按修改时间排序，删除最旧的文件
        cache_files.sort_by_key(|(_, _, modified)| *modified);
        
        for (path, size, _) in cache_files {
            if total_size <= max_size_bytes {
                break;
            }
            
            if let Err(e) = tokio::fs::remove_file(&path).await {
                tracing::warn!("删除缓存文件失败 {}: {}", path.display(), e);
            } else {
                total_size -= size;
                tracing::debug!("删除缓存文件: {}", path.display());
            }
        }

        Ok(())
    }
} 