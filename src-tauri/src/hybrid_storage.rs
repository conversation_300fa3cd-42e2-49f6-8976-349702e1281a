//! Hybrid Storage Engine - SQLite + Operation Log Implementation
//! 
//! This module provides a hybrid storage engine that combines:
//! - SQLite database for current state storage
//! - Operation log for change tracking and sync
//! - LWW (Last-Writer-Wins) conflict resolution using HLC timestamps

use crate::hlc::{H<PERSON>, HLCGenerator};
use crate::models::{ClipboardItem, ContentType, Operation, OperationType, Tombstone, StorageStats};
use anyhow::Result;
use sha2::Digest;
use sqlx::{Sqlite, SqlitePool};
use std::path::{Path, PathBuf};
use std::sync::Arc;

/// Extended statistics for the hybrid storage engine
#[derive(Debug, Clone)]
pub struct HybridStorageStats {
    pub total_items: i64,
    pub active_items: i64,
    pub deleted_items: i64,
    pub total_operations: i64,
    pub database_size: i64,
    pub oplog_size: i64,
    pub last_sync: Option<i64>,
    pub device_id: String,
}

/// Hybrid storage engine that combines SQLite with operation log
pub struct HybridStorageEngine {
    pool: SqlitePool,
    hlc_generator: Arc<HLCGenerator>,
    device_id: String,
    storage_dir: PathBuf,
}

impl HybridStorageEngine {
    /// Create a new hybrid storage engine
    pub async fn new<P: AsRef<Path>>(storage_dir: P, device_id: String) -> Result<Self> {
        let storage_dir = storage_dir.as_ref().to_path_buf();
        tokio::fs::create_dir_all(&storage_dir).await?;
        
        let db_path = storage_dir.join("clippy.db");
        let database_url = format!("sqlite:{}", db_path.display());
        
        let pool = SqlitePool::connect_with(
            sqlx::sqlite::SqliteConnectOptions::new()
                .filename(&db_path)
                .create_if_missing(true)
                .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
                .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
        ).await?;
        
        let engine = Self {
            pool,
            hlc_generator: Arc::new(HLCGenerator::new()),
            device_id,
            storage_dir,
        };
        
        engine.init_database().await?;
        Ok(engine)
    }
    
    /// Initialize the database schema
    async fn init_database(&self) -> Result<()> {
        // Create clipboard_items table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS clipboard_items (
                id TEXT PRIMARY KEY,
                content_type TEXT NOT NULL,
                content TEXT NOT NULL,
                content_hash TEXT NOT NULL,
                hlc_timestamp INTEGER NOT NULL,
                device_id TEXT NOT NULL,
                size INTEGER NOT NULL,
                metadata TEXT,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                deleted_at INTEGER
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create tombstones table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS tombstones (
                item_id TEXT PRIMARY KEY,
                hlc_timestamp INTEGER NOT NULL,
                device_id TEXT NOT NULL,
                created_at INTEGER NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create operations table (oplog)
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS operations (
                hlc_timestamp INTEGER PRIMARY KEY,
                operation_type TEXT NOT NULL,
                item_id TEXT NOT NULL,
                device_id TEXT NOT NULL,
                created_at INTEGER NOT NULL
            )
            "#,
        )
        .execute(&self.pool)
        .await?;
        
        // Create indexes for performance
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_items_content_hash ON clipboard_items(content_hash)")
            .execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_items_hlc ON clipboard_items(hlc_timestamp)")
            .execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_items_device ON clipboard_items(device_id)")
            .execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_ops_hlc ON operations(hlc_timestamp)")
            .execute(&self.pool).await?;
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_ops_device ON operations(device_id)")
            .execute(&self.pool).await?;
        
        Ok(())
    }
    
    /// Add a new clipboard item
    pub async fn add_item(&self, item: ClipboardItem) -> Result<()> {
        let hlc_timestamp = item.timestamp.0 as i64;
        
        // Start a transaction
        let mut tx = self.pool.begin().await?;
        
        // Check for conflicts using LWW resolution
        if let Some(existing) = self.find_existing_item(&item.id).await? {
            if existing.timestamp >= item.timestamp {
                // Existing item is newer or same, ignore this operation
                return Ok(());
            }
        }
        
        // Check if this item is tombstoned
        if let Some(tombstone) = self.find_tombstone(&item.id).await? {
            if tombstone.timestamp >= item.timestamp {
                // Item is tombstoned with newer or equal timestamp, ignore
                return Ok(());
            }
            // Remove old tombstone as we're resurrecting the item
            self.remove_tombstone(&item.id).await?;
        }
        
        // Insert or update the item
        sqlx::query(
            r#"
            INSERT OR REPLACE INTO clipboard_items 
            (id, content_type, content, content_hash, hlc_timestamp, device_id, size, metadata, created_at, updated_at, deleted_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
        )
        .bind(&item.id)
        .bind(item.content_type.as_str())
        .bind(&item.content)
        .bind(&item.content_hash)
        .bind(hlc_timestamp)
        .bind(&item.device_id)
        .bind(item.size)
        .bind(&item.metadata)
        .bind(item.created_at)
        .bind(item.updated_at)
        .bind(item.deleted_at)
        .execute(&mut *tx)
        .await?;
        
        // Add operation to log
        let operation = Operation::new(
            item.timestamp,
            OperationType::Add,
            item.id.clone(),
            item.device_id.clone(),
        );
        self.add_operation_to_log(&mut tx, &operation).await?;
        
        tx.commit().await?;
        Ok(())
    }
    
    /// Delete a clipboard item
    pub async fn delete_item(&self, item_id: &str) -> Result<bool> {
        let timestamp = self.hlc_generator.next();
        let hlc_timestamp = timestamp.0 as i64;
        
        let mut tx = self.pool.begin().await?;
        
        // Check if item exists
        let existing = self.find_existing_item(item_id).await?;
        if existing.is_none() {
            return Ok(false);
        }
        
        // Check if already tombstoned with newer timestamp
        if let Some(tombstone) = self.find_tombstone(item_id).await? {
            if tombstone.timestamp >= timestamp {
                return Ok(false);
            }
        }
        
        // Remove item from active items
        sqlx::query("DELETE FROM clipboard_items WHERE id = ?")
            .bind(item_id)
            .execute(&mut *tx)
            .await?;
        
        // Add tombstone
        let tombstone = Tombstone::new(item_id.to_string(), timestamp, self.device_id.clone());
        sqlx::query(
            "INSERT OR REPLACE INTO tombstones (item_id, hlc_timestamp, device_id, created_at) VALUES (?, ?, ?, ?)"
        )
        .bind(&tombstone.item_id)
        .bind(hlc_timestamp)
        .bind(&tombstone.device_id)
        .bind(tombstone.created_at)
        .execute(&mut *tx)
        .await?;
        
        // Add operation to log
        let operation = Operation::new(
            timestamp,
            OperationType::Delete,
            item_id.to_string(),
            self.device_id.clone(),
        );
        self.add_operation_to_log(&mut tx, &operation).await?;
        
        tx.commit().await?;
        Ok(true)
    }
    
    /// Get all active clipboard items
    pub async fn get_all_items(&self) -> Result<Vec<ClipboardItem>> {
        let rows = sqlx::query(
            r#"
            SELECT id, content_type, content, content_hash, hlc_timestamp, device_id, size, metadata, created_at, updated_at, deleted_at
            FROM clipboard_items 
            WHERE deleted_at IS NULL
            ORDER BY hlc_timestamp DESC
            "#,
        )
        .fetch_all(&self.pool)
        .await?;
        
        let mut items = Vec::new();
        for row in rows {
            let content_type = ContentType::from_str(&row.get::<String, _>("content_type"))
                .ok_or_else(|| anyhow::anyhow!("Invalid content type"))?;
            
            let item = ClipboardItem {
                id: row.get("id"),
                content_type,
                content: row.get("content"),
                content_hash: row.get("content_hash"),
                timestamp: HLC(row.get::<i64, _>("hlc_timestamp") as u64),
                device_id: row.get("device_id"),
                size: row.get("size"),
                metadata: row.get("metadata"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
                deleted_at: row.get("deleted_at"),
            };
            items.push(item);
        }
        
        Ok(items)
    }
    
    /// Find item by content hash
    pub async fn find_by_content_hash(&self, hash: &str) -> Result<Option<ClipboardItem>> {
        let row = sqlx::query(
            "SELECT id, content_type, content, content_hash, hlc_timestamp, device_id, size, metadata, created_at, updated_at, deleted_at FROM clipboard_items WHERE content_hash = ? AND deleted_at IS NULL LIMIT 1"
        )
        .bind(hash)
        .fetch_optional(&self.pool)
        .await?;
        
        if let Some(row) = row {
            let content_type = ContentType::from_str(&row.get::<String, _>("content_type"))
                .ok_or_else(|| anyhow::anyhow!("Invalid content type"))?;
            
            let item = ClipboardItem {
                id: row.get("id"),
                content_type,
                content: row.get("content"),
                content_hash: row.get("content_hash"),
                timestamp: HLC(row.get::<i64, _>("hlc_timestamp") as u64),
                device_id: row.get("device_id"),
                size: row.get("size"),
                metadata: row.get("metadata"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
                deleted_at: row.get("deleted_at"),
            };
            Ok(Some(item))
        } else {
            Ok(None)
        }
    }
    
    /// Get operations since a given timestamp
    pub async fn get_operations_since(&self, since: HLC) -> Result<Vec<Operation>> {
        let since_timestamp = since.0 as i64;
        
        let rows = sqlx::query(
            "SELECT hlc_timestamp, operation_type, item_id, device_id, created_at FROM operations WHERE hlc_timestamp > ? ORDER BY hlc_timestamp"
        )
        .bind(since_timestamp)
        .fetch_all(&self.pool)
        .await?;
        
        let mut operations = Vec::new();
        for row in rows {
            let operation_type = OperationType::from_str(&row.get::<String, _>("operation_type"))
                .ok_or_else(|| anyhow::anyhow!("Invalid operation type"))?;
            
            let operation = Operation {
                timestamp: HLC(row.get::<i64, _>("hlc_timestamp") as u64),
                operation_type,
                item_id: row.get("item_id"),
                device_id: row.get("device_id"),
                created_at: row.get("created_at"),
            };
            operations.push(operation);
        }
        
        Ok(operations)
    }
    
    /// Get storage statistics
    pub async fn get_stats(&self) -> Result<StorageStats> {
        let mut stats = StorageStats::default();
        stats.device_id = self.device_id.clone();
        
        // Count items
        let counts = sqlx::query("SELECT COUNT(*) as total, SUM(CASE WHEN deleted_at IS NULL THEN 1 ELSE 0 END) as active FROM clipboard_items")
            .fetch_one(&self.pool)
            .await?;
        stats.total_items = counts.get("total");
        stats.active_items = counts.get("active");
        
        // Count tombstones
        let tombstone_count = sqlx::query("SELECT COUNT(*) as count FROM tombstones")
            .fetch_one(&self.pool)
            .await?;
        stats.deleted_items = tombstone_count.get("count");
        
        // Count operations
        let op_count = sqlx::query("SELECT COUNT(*) as count FROM operations")
            .fetch_one(&self.pool)
            .await?;
        stats.total_operations = op_count.get("count");
        
        // Get database size
        let db_path = self.storage_dir.join("clippy.db");
        if let Ok(metadata) = tokio::fs::metadata(&db_path).await {
            stats.database_size = metadata.len() as i64;
        }
        
        Ok(stats)
    }
    
    /// Clear all data
    pub async fn clear_all(&self) -> Result<()> {
        let mut tx = self.pool.begin().await?;
        
        sqlx::query("DELETE FROM clipboard_items").execute(&mut *tx).await?;
        sqlx::query("DELETE FROM tombstones").execute(&mut *tx).await?;
        sqlx::query("DELETE FROM operations").execute(&mut *tx).await?;
        
        tx.commit().await?;
        Ok(())
    }
    
    /// Apply LWW conflict resolution for sync
    pub async fn apply_sync_operations(&self, operations: Vec<Operation>) -> Result<usize> {
        let mut applied_count = 0;
        
        for operation in operations {
            match operation.operation_type {
                OperationType::Add => {
                    // For ADD operations, we need the actual item data
                    // This would typically come from the sync process
                    applied_count += 1;
                }
                OperationType::Delete => {
                    // Apply delete operation
                    let hlc_timestamp = operation.timestamp.0 as i64;
                    
                    // Check if we should apply this delete
                    if let Some(existing) = self.find_existing_item(&operation.item_id).await? {
                        if operation.timestamp > existing.timestamp {
                            let mut tx = self.pool.begin().await?;
                            
                            // Remove item
                            sqlx::query("DELETE FROM clipboard_items WHERE id = ?")
                                .bind(&operation.item_id)
                                .execute(&mut *tx)
                                .await?;
                            
                            // Add tombstone
                            let tombstone = Tombstone::new(
                                operation.item_id.clone(),
                                operation.timestamp,
                                operation.device_id.clone(),
                            );
                            sqlx::query(
                                "INSERT OR REPLACE INTO tombstones (item_id, hlc_timestamp, device_id, created_at) VALUES (?, ?, ?, ?)"
                            )
                            .bind(&tombstone.item_id)
                            .bind(hlc_timestamp)
                            .bind(&tombstone.device_id)
                            .bind(tombstone.created_at)
                            .execute(&mut *tx)
                            .await?;
                            
                            // Add operation to log
                            self.add_operation_to_log(&mut tx, &operation).await?;
                            
                            tx.commit().await?;
                            applied_count += 1;
                        }
                    }
                }
            }
        }
        
        Ok(applied_count)
    }
    
    /// Update HLC generator with external timestamp
    pub fn update_hlc(&self, external_hlc: HLC) {
        self.hlc_generator.update(external_hlc);
    }
    
    /// Get next HLC timestamp
    pub fn next_hlc(&self) -> HLC {
        self.hlc_generator.next()
    }
    
    /// Get device ID
    pub fn device_id(&self) -> &str {
        &self.device_id
    }
    
    // Helper methods
    
    async fn find_existing_item(&self, item_id: &str) -> Result<Option<ClipboardItem>> {
        let row = sqlx::query(
            "SELECT id, content_type, content, content_hash, hlc_timestamp, device_id, size, metadata, created_at, updated_at, deleted_at FROM clipboard_items WHERE id = ?"
        )
        .bind(item_id)
        .fetch_optional(&self.pool)
        .await?;
        
        if let Some(row) = row {
            let content_type = ContentType::from_str(&row.get::<String, _>("content_type"))
                .ok_or_else(|| anyhow::anyhow!("Invalid content type"))?;
            
            let item = ClipboardItem {
                id: row.get("id"),
                content_type,
                content: row.get("content"),
                content_hash: row.get("content_hash"),
                timestamp: HLC(row.get::<i64, _>("hlc_timestamp") as u64),
                device_id: row.get("device_id"),
                size: row.get("size"),
                metadata: row.get("metadata"),
                created_at: row.get("created_at"),
                updated_at: row.get("updated_at"),
                deleted_at: row.get("deleted_at"),
            };
            Ok(Some(item))
        } else {
            Ok(None)
        }
    }
    
    async fn find_tombstone(&self, item_id: &str) -> Result<Option<Tombstone>> {
        let row = sqlx::query(
            "SELECT item_id, hlc_timestamp, device_id, created_at FROM tombstones WHERE item_id = ?"
        )
        .bind(item_id)
        .fetch_optional(&self.pool)
        .await?;
        
        if let Some(row) = row {
            let tombstone = Tombstone {
                item_id: row.get("item_id"),
                timestamp: HLC(row.get::<i64, _>("hlc_timestamp") as u64),
                device_id: row.get("device_id"),
                created_at: row.get("created_at"),
            };
            Ok(Some(tombstone))
        } else {
            Ok(None)
        }
    }
    
    async fn remove_tombstone(&self, item_id: &str) -> Result<()> {
        sqlx::query("DELETE FROM tombstones WHERE item_id = ?")
            .bind(item_id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }
    
    async fn add_operation_to_log(&self, tx: &mut sqlx::Transaction<'_, Sqlite>, operation: &Operation) -> Result<()> {
        let hlc_timestamp = operation.timestamp.0 as i64;
        
        sqlx::query(
            "INSERT OR REPLACE INTO operations (hlc_timestamp, operation_type, item_id, device_id, created_at) VALUES (?, ?, ?, ?, ?)"
        )
        .bind(hlc_timestamp)
        .bind(operation.operation_type.as_str())
        .bind(&operation.item_id)
        .bind(&operation.device_id)
        .bind(operation.created_at)
        .execute(&mut **tx)
        .await?;
        
        Ok(())
    }
} 